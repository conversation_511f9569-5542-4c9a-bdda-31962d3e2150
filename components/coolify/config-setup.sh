#!/bin/sh
# Coolify platform setup
set -e

echo "🚀 Setting up Coolify platform..."

# Generate secure random values for Coolify
generate_random_hex() {
    openssl rand -hex 16
}

generate_random_base64() {
    openssl rand -base64 32
}

# Generate required values if not provided
APP_ID="${APP_ID:-$(generate_random_hex)}"
APP_KEY="${APP_KEY:-base64:$(generate_random_base64)}"

echo "📁 Creating Coolify directory structure..."

# Create all necessary directories as per official Coolify setup
mkdir -p /host-setup/data/coolify/{source,ssh,applications,databases,backups,services,proxy,webhooks-during-maintenance}
mkdir -p /host-setup/data/coolify/ssh/{keys,mux}
mkdir -p /host-setup/data/coolify/proxy/dynamic

echo "🔑 Setting up SSH keys..."

# Generate SSH key for Coolify to manage the server
if [ ! -f "/host-setup/data/coolify/ssh/keys/<EMAIL>" ]; then
    if command -v ssh-keygen >/dev/null 2>&1; then
        ssh-keygen -f /host-setup/data/coolify/ssh/keys/<EMAIL> -t ed25519 -N '' -C root@coolify
        echo "✅ Generated SSH key for Coolify"
    else
        echo "⚠️ ssh-keygen not available in container. SSH key generation will be handled in server prerequisites."
        # Create placeholder files so the directory structure is correct
        mkdir -p /host-setup/data/coolify/ssh/keys
        touch /host-setup/data/coolify/ssh/keys/.placeholder
    fi
else
    echo "✅ SSH key already exists"
fi

echo "📝 Creating Coolify .env file in root directory..."

# Create Coolify .env file in the root directory (following project convention)
cat > /host-setup/.env << EOF
# Coolify Configuration - Generated $(date)
#
# Prerequisites for server setup (run these commands on your server before docker compose up):
#
# 1. Create directories:
#    mkdir -p /data/coolify/{source,ssh,applications,databases,backups,services,proxy,webhooks-during-maintenance}
#    mkdir -p /data/coolify/ssh/{keys,mux}
#    mkdir -p /data/coolify/proxy/dynamic
#
# 2. Generate & add SSH key:
#    ssh-keygen -f /data/coolify/ssh/keys/<EMAIL> -t ed25519 -N '' -C root@coolify
#    cat /data/coolify/ssh/keys/<EMAIL> >> ~/.ssh/authorized_keys
#    chmod 600 ~/.ssh/authorized_keys
#
# 3. Set permissions:
#    chown -R 9999:root /data/coolify
#    chmod -R 700 /data/coolify
#
# 4. Create Docker network:
#    docker network create --attachable coolify

# Application Configuration
APP_ID=${APP_ID}
APP_KEY=${APP_KEY}
APP_NAME=${APP_NAME:-Coolify}
APP_ENV=${APP_ENV:-production}
APP_DEBUG=false
APP_URL=http://localhost:${APP_PORT:-8000}

# Database Configuration
DB_CONNECTION=pgsql
DB_HOST=postgres
DB_PORT=5432
DB_DATABASE=${DB_DATABASE:-coolify}
DB_USERNAME=${DB_USERNAME}
DB_PASSWORD=${DB_PASSWORD}

# Redis Configuration
REDIS_HOST=redis
REDIS_PASSWORD=${REDIS_PASSWORD}
REDIS_PORT=6379

# Pusher Configuration (for WebSocket)
PUSHER_APP_ID=${PUSHER_APP_ID}
PUSHER_APP_KEY=${PUSHER_APP_KEY}
PUSHER_APP_SECRET=${PUSHER_APP_SECRET}
PUSHER_HOST=soketi
PUSHER_PORT=6001
PUSHER_SCHEME=http

# Docker Configuration
DOCKER_HOST=unix:///var/run/docker.sock

# SSL Configuration
SSL_MODE=off

# Session Configuration
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# Cache Configuration
CACHE_DRIVER=redis

# Queue Configuration
QUEUE_CONNECTION=redis

# Mail Configuration (optional)
MAIL_MAILER=log

# Logging
LOG_CHANNEL=stack
LOG_LEVEL=info

# Additional Coolify specific settings
COOLIFY_AUTO_UPDATE=false
COOLIFY_INSTANCE_SETTINGS_IS_REGISTRATION_ENABLED=false

# Registry Configuration (optional)
REGISTRY_URL=${REGISTRY_URL:-ghcr.io}
LATEST_IMAGE=${LATEST_IMAGE:-latest}

# PHP Configuration
PHP_MEMORY_LIMIT=${PHP_MEMORY_LIMIT:-256M}
PHP_FPM_PM_CONTROL=${PHP_FPM_PM_CONTROL:-dynamic}
PHP_FPM_PM_START_SERVERS=${PHP_FPM_PM_START_SERVERS:-1}
PHP_FPM_PM_MIN_SPARE_SERVERS=${PHP_FPM_PM_MIN_SPARE_SERVERS:-1}
PHP_FPM_PM_MAX_SPARE_SERVERS=${PHP_FPM_PM_MAX_SPARE_SERVERS:-10}

# Port Configuration
APP_PORT=${APP_PORT:-8000}
SOKETI_PORT=${SOKETI_PORT:-6001}

# Soketi Configuration
SOKETI_DEBUG=${SOKETI_DEBUG:-false}
EOF

echo "✅ Created Coolify .env configuration in root directory"

echo "🌐 Setting up Docker network..."

# Create the coolify docker network if it doesn't exist
if ! docker network ls | grep -q "coolify"; then
    echo "Creating coolify docker network..."
    docker network create --attachable coolify || echo "⚠️ Network coolify may already exist"
else
    echo "✅ Coolify docker network already exists"
fi

echo "📋 Setting up permissions..."

# Set correct permissions for Coolify directories
chown -R 9999:root /host-setup/data/coolify 2>/dev/null || echo "⚠️ Could not set ownership (may need to run on host)"
chmod -R 700 /host-setup/data/coolify 2>/dev/null || echo "⚠️ Could not set permissions (may need to run on host)"

echo "✅ Coolify platform setup complete"
echo ""
echo "🚨 IMPORTANT: Before running 'docker compose up -d', ensure you have:"
echo "   1. Run the server prerequisites listed in the .env file comments"
echo "   2. Added the SSH public key to your ~/.ssh/authorized_keys"
echo "   3. Set proper permissions on /data/coolify directories"
echo "   4. Created the coolify Docker network"
echo ""


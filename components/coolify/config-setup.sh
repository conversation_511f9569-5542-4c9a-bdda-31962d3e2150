#!/bin/sh
# Coolify platform setup
set -e

echo "🚀 Setting up Coolify platform..."

# Generate secure random values for Coolify
generate_random_hex() {
    openssl rand -hex 16
}

generate_random_base64() {
    openssl rand -base64 32
}

# Generate required values if not provided
APP_ID="${APP_ID:-$(generate_random_hex)}"
APP_KEY="${APP_KEY:-base64:$(generate_random_base64)}"

echo "📁 Creating Coolify directory structure..."

# Create all necessary directories as per official Coolify setup
mkdir -p /host-setup/data/coolify/{source,ssh,applications,databases,backups,services,proxy,webhooks-during-maintenance}
mkdir -p /host-setup/data/coolify/ssh/{keys,mux}
mkdir -p /host-setup/data/coolify/proxy/dynamic

echo "🔑 Setting up SSH keys..."

# Generate SSH key for Coolify to manage the server
if [ ! -f "/host-setup/data/coolify/ssh/keys/<EMAIL>" ]; then
    if command -v ssh-keygen >/dev/null 2>&1; then
        ssh-keygen -f /host-setup/data/coolify/ssh/keys/<EMAIL> -t ed25519 -N '' -C root@coolify
        echo "✅ Generated SSH key for Coolify"
    else
        echo "⚠️ ssh-keygen not available in container. SSH key generation will be handled in server prerequisites."
        # Create placeholder files so the directory structure is correct
        mkdir -p /host-setup/data/coolify/ssh/keys
        touch /host-setup/data/coolify/ssh/keys/.placeholder
    fi
else
    echo "✅ SSH key already exists"
fi

echo "🔧 Auto-generating missing Coolify environment variables..."

# Auto-generate missing values if not provided
if [ -z "${APP_ID:-}" ]; then
    export APP_ID=$(generate_random_hex)
    echo "Generated APP_ID"
fi

if [ -z "${APP_KEY:-}" ]; then
    export APP_KEY="base64:$(generate_random_base64)"
    echo "Generated APP_KEY"
fi

if [ -z "${DB_USERNAME:-}" ]; then
    export DB_USERNAME="coolify"
    echo "Set default DB_USERNAME=coolify"
fi

if [ -z "${DB_PASSWORD:-}" ]; then
    export DB_PASSWORD=$(generate_random_base64)
    echo "Generated DB_PASSWORD"
fi

if [ -z "${REDIS_PASSWORD:-}" ]; then
    export REDIS_PASSWORD=$(generate_random_base64)
    echo "Generated REDIS_PASSWORD"
fi

if [ -z "${PUSHER_APP_ID:-}" ]; then
    export PUSHER_APP_ID=$(openssl rand -hex 32)
    echo "Generated PUSHER_APP_ID"
fi

if [ -z "${PUSHER_APP_KEY:-}" ]; then
    export PUSHER_APP_KEY=$(openssl rand -hex 32)
    echo "Generated PUSHER_APP_KEY"
fi

if [ -z "${PUSHER_APP_SECRET:-}" ]; then
    export PUSHER_APP_SECRET=$(openssl rand -hex 32)
    echo "Generated PUSHER_APP_SECRET"
fi

echo "✅ Auto-generated missing Coolify configuration values"

echo "🌐 Setting up Docker network..."

# Create the coolify docker network if it doesn't exist
if ! docker network ls | grep -q "coolify"; then
    echo "Creating coolify docker network..."
    docker network create --attachable coolify || echo "⚠️ Network coolify may already exist"
else
    echo "✅ Coolify docker network already exists"
fi

echo "📋 Setting up permissions..."

# Set correct permissions for Coolify directories
chown -R 9999:root /host-setup/data/coolify 2>/dev/null || echo "⚠️ Could not set ownership (may need to run on host)"
chmod -R 700 /host-setup/data/coolify 2>/dev/null || echo "⚠️ Could not set permissions (may need to run on host)"

echo "✅ Coolify platform setup complete"
echo ""
echo "🚨 IMPORTANT: Before running 'docker compose up -d', ensure you have:"
echo "   1. Run the server prerequisites listed in the compose.yaml file comments"
echo "   2. Added the SSH public key to your ~/.ssh/authorized_keys"
echo "   3. Set proper permissions on /data/coolify directories"
echo "   4. Created the coolify Docker network"
echo ""


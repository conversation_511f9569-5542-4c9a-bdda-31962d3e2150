#!/bin/sh
# Coolify platform setup
set -e

echo "🚀 Setting up Coolify platform..."

# Generate secure random values for Coolify
generate_random_hex() {
    openssl rand -hex 16
}

generate_random_base64() {
    openssl rand -base64 32
}

# Generate required values if not provided
APP_ID="${APP_ID:-$(generate_random_hex)}"
APP_KEY="${APP_KEY:-base64:$(generate_random_base64)}"

echo "📁 Creating Coolify directory structure..."

# Create all necessary directories as per official Coolify setup
mkdir -p /host-setup/data/coolify/{source,ssh,applications,databases,backups,services,proxy,webhooks-during-maintenance}
mkdir -p /host-setup/data/coolify/ssh/{keys,mux}
mkdir -p /host-setup/data/coolify/proxy/dynamic

echo "🔑 Setting up SSH keys..."

# Generate SSH key for Coolify to manage the server
if [ ! -f "/host-setup/data/coolify/ssh/keys/<EMAIL>" ]; then
    if command -v ssh-keygen >/dev/null 2>&1; then
        ssh-keygen -f /host-setup/data/coolify/ssh/keys/<EMAIL> -t ed25519 -N '' -C root@coolify
        echo "✅ Generated SSH key for Coolify"
    else
        echo "⚠️ ssh-keygen not available in container. SSH key generation will be handled in server prerequisites."
        # Create placeholder files so the directory structure is correct
        mkdir -p /host-setup/data/coolify/ssh/keys
        touch /host-setup/data/coolify/ssh/keys/.placeholder
    fi
else
    echo "✅ SSH key already exists"
fi

echo "🔧 Auto-generating missing Coolify environment variables..."

# Check if .env file exists, if not create it
if [ ! -f "/host-setup/.env" ]; then
    echo "Creating .env file..."
    touch /host-setup/.env
fi

# Function to update or add environment variable in .env file
update_env_var() {
    local var_name="$1"
    local var_value="$2"
    local env_file="/host-setup/.env"

    if grep -q "^${var_name}=" "$env_file"; then
        # Variable exists, check if it's empty
        if grep -q "^${var_name}=$" "$env_file" || grep -q "^${var_name}=\"\"$" "$env_file"; then
            # Variable is empty, update it
            sed -i "s|^${var_name}=.*|${var_name}=${var_value}|" "$env_file"
            echo "Generated ${var_name}"
        else
            echo "Using existing ${var_name}"
        fi
    else
        # Variable doesn't exist, add it
        echo "${var_name}=${var_value}" >> "$env_file"
        echo "Generated ${var_name}"
    fi
}

# Auto-generate missing values if not provided
APP_ID=$(generate_random_hex)
update_env_var "APP_ID" "$APP_ID"
export APP_ID

APP_KEY="base64:$(generate_random_base64)"
update_env_var "APP_KEY" "$APP_KEY"
export APP_KEY

if [ -z "${DB_USERNAME:-}" ]; then
    DB_USERNAME="coolify"
    update_env_var "DB_USERNAME" "$DB_USERNAME"
    export DB_USERNAME
fi

if [ -z "${DB_PASSWORD:-}" ]; then
    DB_PASSWORD=$(generate_random_base64)
    update_env_var "DB_PASSWORD" "$DB_PASSWORD"
    export DB_PASSWORD
fi

if [ -z "${REDIS_PASSWORD:-}" ]; then
    REDIS_PASSWORD=$(generate_random_base64)
    update_env_var "REDIS_PASSWORD" "$REDIS_PASSWORD"
    export REDIS_PASSWORD
fi

if [ -z "${PUSHER_APP_ID:-}" ]; then
    PUSHER_APP_ID=$(openssl rand -hex 32)
    update_env_var "PUSHER_APP_ID" "$PUSHER_APP_ID"
    export PUSHER_APP_ID
fi

if [ -z "${PUSHER_APP_KEY:-}" ]; then
    PUSHER_APP_KEY=$(openssl rand -hex 32)
    update_env_var "PUSHER_APP_KEY" "$PUSHER_APP_KEY"
    export PUSHER_APP_KEY
fi

if [ -z "${PUSHER_APP_SECRET:-}" ]; then
    PUSHER_APP_SECRET=$(openssl rand -hex 32)
    update_env_var "PUSHER_APP_SECRET" "$PUSHER_APP_SECRET"
    export PUSHER_APP_SECRET
fi

# Add other common Coolify variables with defaults if they don't exist
update_env_var "APP_NAME" "${APP_NAME:-Coolify}"
update_env_var "APP_ENV" "${APP_ENV:-production}"
update_env_var "DB_DATABASE" "${DB_DATABASE:-coolify}"
update_env_var "APP_PORT" "${APP_PORT:-8000}"
update_env_var "SOKETI_PORT" "${SOKETI_PORT:-6001}"
update_env_var "SOKETI_DEBUG" "${SOKETI_DEBUG:-false}"
update_env_var "REGISTRY_URL" "${REGISTRY_URL:-ghcr.io}"
update_env_var "LATEST_IMAGE" "${LATEST_IMAGE:-latest}"

echo "✅ Auto-generated missing Coolify configuration values and updated .env file"

echo "🌐 Setting up Docker network..."

# Create the coolify docker network if it doesn't exist
if ! docker network ls | grep -q "coolify"; then
    echo "Creating coolify docker network..."
    docker network create --attachable coolify || echo "⚠️ Network coolify may already exist"
else
    echo "✅ Coolify docker network already exists"
fi

echo "📋 Setting up permissions..."

# Set correct permissions for Coolify directories
chown -R 9999:root /host-setup/data/coolify 2>/dev/null || echo "⚠️ Could not set ownership (may need to run on host)"
chmod -R 700 /host-setup/data/coolify 2>/dev/null || echo "⚠️ Could not set permissions (may need to run on host)"

echo "✅ Coolify platform setup complete"
echo ""
echo "🚨 IMPORTANT: Before running 'docker compose up -d', ensure you have:"
echo "   1. Run the server prerequisites listed in the compose.yaml file comments"
echo "   2. Added the SSH public key to your ~/.ssh/authorized_keys"
echo "   3. Set proper permissions on /data/coolify directories"
echo "   4. Created the coolify Docker network"
echo ""


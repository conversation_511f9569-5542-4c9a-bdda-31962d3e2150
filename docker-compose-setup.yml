services:
  # Setup container that creates folder structure and config files
  setup:
    image: alpine:latest
    #container_name: pangolin-setup
    volumes:
      - ./:/host-setup
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      # Pangolin platform variables
      - DOMAIN=${DOMAIN:-}
      - EMAIL=${EMAIL:-}
      - ADMIN_USERNAME=${ADMIN_USERNAME:-}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-}
      - ADMIN_SUBDOMAIN=${ADMIN_SUBDOMAIN:-pangolin}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_HOST=${POSTGRES_HOST:-pangolin-postgres}
      # Optional custom subdomains for pangolin+ components
      - MIDDLEWARE_MANAGER_SUBDOMAIN=${MIDDLEWARE_MANAGER_SUBDOMAIN:-}
      - TRAEFIK_SUBDOMAIN=${TRAEFIK_SUBDOMAIN:-}
      - KOMODO_SUBDOMAIN=${KOMODO_SUBDOMAIN:-}
      - NLWEB_SUBDOMAIN=${NLWEB_SUBDOMAIN:-}
      - LOGS_SUBDOMAIN=${LOGS_SUBDOMAIN:-}
      # Coolify platform variables
      - DB_USERNAME=${DB_USERNAME:-}
      - DB_PASSWORD=${DB_PASSWORD:-}
      - DB_DATABASE=${DB_DATABASE:-}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - PUSHER_APP_ID=${PUSHER_APP_ID:-}
      - PUSHER_APP_KEY=${PUSHER_APP_KEY:-}
      - PUSHER_APP_SECRET=${PUSHER_APP_SECRET:-}
      - REGISTRY_URL=${REGISTRY_URL:-}
      - LATEST_IMAGE=${LATEST_IMAGE:-}
      - APP_ENV=${APP_ENV:-}
      - PHP_MEMORY_LIMIT=${PHP_MEMORY_LIMIT:-}
      - APP_PORT=${APP_PORT:-}
      - SOKETI_PORT=${SOKETI_PORT:-}
      - APP_NAME=${APP_NAME:-}
      - SOKETI_DEBUG=${SOKETI_DEBUG:-}
      # Optional features
      - CROWDSEC_ENROLLMENT_KEY=${CROWDSEC_ENROLLMENT_KEY:-}
      - STATIC_PAGE_SUBDOMAIN=${STATIC_PAGE_SUBDOMAIN:-}
      - MAXMIND_LICENSE_KEY=${MAXMIND_LICENSE_KEY:-}
      # OAuth configuration (if needed)
      - CLIENT_ID=${CLIENT_ID:-}
      - CLIENT_SECRET=${CLIENT_SECRET:-}
      # Komodo integration (if needed)
      - KOMODO_HOST_IP=${KOMODO_HOST_IP:-} # if set you must use komodo-postgres-1 as the POSTGRES_HOST
      # OpenAI API Key for nlweb (if needed)
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      # Orchestrator selection (optional). If unset, it will be auto-derived from provided env vars.
      - COMPONENTS=${COMPONENTS:-}
      # Additional optional variables used by components
      - OAUTH_DOMAIN=${OAUTH_DOMAIN:-oauth.${DOMAIN}}
      - COMPOSE_KOMODO_IMAGE_TAG=${COMPOSE_KOMODO_IMAGE_TAG:-latest}
      - TZ=${TZ:-Etc/UTC}
      - KOMODO_PASSKEY=${KOMODO_PASSKEY:-}
      - KOMODO_TITLE=${KOMODO_TITLE:-}
      - KOMODO_FIRST_SERVER=${KOMODO_FIRST_SERVER:-}
      - KOMODO_DISABLE_CONFIRM_DIALOG=${KOMODO_DISABLE_CONFIRM_DIALOG:-}
      - KOMODO_MONITORING_INTERVAL=${KOMODO_MONITORING_INTERVAL:-}
      - KOMODO_RESOURCE_POLL_INTERVAL=${KOMODO_RESOURCE_POLL_INTERVAL:-}
      - KOMODO_WEBHOOK_SECRET=${KOMODO_WEBHOOK_SECRET:-}
      - KOMODO_JWT_SECRET=${KOMODO_JWT_SECRET:-}
      - KOMODO_JWT_TTL=${KOMODO_JWT_TTL:-}
      - KOMODO_LOCAL_AUTH=${KOMODO_LOCAL_AUTH:-}
      - KOMODO_DISABLE_USER_REGISTRATION=${KOMODO_DISABLE_USER_REGISTRATION:-}
      - KOMODO_ENABLE_NEW_USERS=${KOMODO_ENABLE_NEW_USERS:-}
      - KOMODO_DISABLE_NON_ADMIN_CREATE=${KOMODO_DISABLE_NON_ADMIN_CREATE:-}
      - KOMODO_TRANSPARENT_MODE=${KOMODO_TRANSPARENT_MODE:-}
      - KOMODO_LOGGING_PRETTY=${KOMODO_LOGGING_PRETTY:-}
      - KOMODO_PRETTY_STARTUP_CONFIG=${KOMODO_PRETTY_STARTUP_CONFIG:-}
      - KOMODO_OIDC_ENABLED=${KOMODO_OIDC_ENABLED:-}
      - KOMODO_GITHUB_OAUTH_ENABLED=${KOMODO_GITHUB_OAUTH_ENABLED:-}
      - KOMODO_GOOGLE_OAUTH_ENABLED=${KOMODO_GOOGLE_OAUTH_ENABLED:-}
      - KOMODO_AWS_ACCESS_KEY_ID=${KOMODO_AWS_ACCESS_KEY_ID:-}
      - KOMODO_AWS_SECRET_ACCESS_KEY=${KOMODO_AWS_SECRET_ACCESS_KEY:-}

    command: |
      sh -c "
        echo '🚀 Starting Manidae setup container (modular orchestrator only)...'

        # Install required tools
        apk add --no-cache bash gettext curl docker-cli openssl

        # Validate required environment variables based on platform
        PANGOLIN_VARS_SET=false
        COOLIFY_VARS_SET=false

        if [ -n \"$$DOMAIN\" ] && [ -n \"$$EMAIL\" ]; then
          PANGOLIN_VARS_SET=true
        fi

        if [ -n \"$$DB_USERNAME\" ] && [ -n \"$$DB_PASSWORD\" ] && [ -n \"$$REDIS_PASSWORD\" ] && [ -n \"$$PUSHER_APP_ID\" ] && [ -n \"$$PUSHER_APP_KEY\" ] && [ -n \"$$PUSHER_APP_SECRET\" ]; then
          COOLIFY_VARS_SET=true
        fi

        if [ \"$$PANGOLIN_VARS_SET\" = false ] && [ \"$$COOLIFY_VARS_SET\" = false ]; then
          echo '❌ Error: Required environment variables not set for any platform!'
          echo ''
          echo '🐧 For Pangolin platform:'
          echo 'DOMAIN=example.com EMAIL=<EMAIL> ADMIN_USERNAME=<EMAIL> ADMIN_PASSWORD=mypassword docker compose -f docker-compose-setup.yml up'
          echo 'Required: DOMAIN, EMAIL'
          echo 'Optional: ADMIN_SUBDOMAIN, ADMIN_USERNAME, ADMIN_PASSWORD'
          echo ''
          echo '🚀 For Coolify platform:'
          echo 'DB_USERNAME=coolify DB_PASSWORD=secure-password REDIS_PASSWORD=redis-password PUSHER_APP_ID=app-id PUSHER_APP_KEY=app-key PUSHER_APP_SECRET=app-secret docker compose -f docker-compose-setup.yml up'
          echo 'Required: DB_USERNAME, DB_PASSWORD, REDIS_PASSWORD, PUSHER_APP_ID, PUSHER_APP_KEY, PUSHER_APP_SECRET'
          echo 'Optional: DB_DATABASE, REGISTRY_URL, LATEST_IMAGE, APP_ENV, APP_PORT, etc.'
          exit 1
        fi

        # Check if config folder already exists
        if [ -d \"/host-setup/config\" ]; then
          echo '⚠️ Config folder already exists!'
          echo 'To avoid overwriting your configuration, setup will not proceed.'
          echo 'If you want to run setup again, please remove or rename the existing config folder.'
          exit 1
        fi

        # Validate domain and email format (only for Pangolin platform)
        if [ \"$$PANGOLIN_VARS_SET\" = true ]; then
          if ! echo \"$$DOMAIN\" | grep -E '^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\\.[a-zA-Z]{2,}$$' > /dev/null; then
            echo '❌ Error: Invalid domain format'
            exit 1
          fi

          if ! echo \"$$EMAIL\" | grep -E '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$$' > /dev/null; then
            echo '❌ Error: Invalid email format'
            exit 1
          fi
        fi

        # Validate [optional] username and password (only for Pangolin platform)
        if [ \"$$PANGOLIN_VARS_SET\" = true ]; then
          if [ ! -z \"$$ADMIN_USERNAME\" ] && [ \"$$ADMIN_USERNAME\" != \"None\" ] && ! echo \"$$ADMIN_USERNAME\" | grep -E '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$$' > /dev/null; then
            echo '❌ Error: Invalid admin username email format'
            exit 1
          fi

          if [ ! -z \"$$ADMIN_PASSWORD\" ] && [ \"$$ADMIN_PASSWORD\" != \"None\" ] &&  [ $${#ADMIN_PASSWORD} -lt 8 ]; then
            echo '❌ Error: Password must be at least 8 characters long'
            exit 1
          fi
        fi

        echo '✅ Environment variables validated'

        # Run modular orchestrator (refactored system)
        if [ -f "/host-setup/orchestrator/build-compose.sh" ]; then
          echo '🔧 Running modular orchestrator...'
          # Ensure required tools for orchestrator and generated scripts
          apk add --no-cache bash gettext curl docker-cli openssl || true

          # Derive COMPONENTS if not provided
          if [ -z \"$$COMPONENTS\" ]; then
            echo \"[orchestrator] Auto-deriving COMPONENTS...\"

            # Detect base platform
            if [ -n \"$$DB_USERNAME\" ] && [ -n \"$$REDIS_PASSWORD\" ] && [ -n \"$$PUSHER_APP_ID\" ]; then
              echo \"[orchestrator] Detected Coolify platform (DB_USERNAME, REDIS_PASSWORD, PUSHER_APP_ID are set)\"
              COMPONENTS=\"coolify\"
            elif [ -n \"$$DOMAIN\" ] && [ -n \"$$EMAIL\" ]; then
              echo \"[orchestrator] Detected Pangolin platform (DOMAIN and EMAIL are set)\"
              COMPONENTS=\"pangolin,middleware-manager\"
            else
              echo \"[orchestrator] ERROR: Could not detect platform. Please provide either:\"
              echo \"  - For Pangolin: DOMAIN and EMAIL\"
              echo \"  - For Coolify: DB_USERNAME, REDIS_PASSWORD, and PUSHER_APP_ID\"
              exit 1
            fi

            # Add optional components based on environment variables
            if [ -n \"$$CROWDSEC_ENROLLMENT_KEY\" ]; then
              echo \"[orchestrator] Adding crowdsec (CROWDSEC_ENROLLMENT_KEY is set)\"
              COMPONENTS=\"$$COMPONENTS,crowdsec\"
            fi
            if [ -n \"$$CLIENT_ID\" ] && [ -n \"$$CLIENT_SECRET\" ]; then
              echo \"[orchestrator] Adding mcpauth (CLIENT_ID and CLIENT_SECRET are set)\"
              COMPONENTS=\"$$COMPONENTS,mcpauth\"
            fi
            if [ -n \"$$OPENAI_API_KEY\" ] || [ -n \"$$AZURE_OPENAI_API_KEY\" ] || [ -n \"$$ANTHROPIC_API_KEY\" ] || [ -n \"$$GEMINI_API_KEY\" ]; then
              echo \"[orchestrator] Adding nlweb (AI_API_KEY is set)\"
              COMPONENTS=\"$$COMPONENTS,nlweb\"
            fi
            if [ -n \"$$KOMODO_HOST_IP\" ]; then
              echo \"[orchestrator] Adding komodo (KOMODO_HOST_IP is set)\"
              COMPONENTS=\"$$COMPONENTS,komodo\"
            fi
            if [ -n \"$$STATIC_PAGE_SUBDOMAIN\" ]; then
              echo \"[orchestrator] Adding static-page (STATIC_PAGE_SUBDOMAIN is set)\"
              COMPONENTS=\"$$COMPONENTS,static-page\"
            fi
            if [ -n \"$$MAXMIND_LICENSE_KEY\" ]; then
              echo \"[orchestrator] Adding traefik-log-dashboard (MAXMIND_LICENSE_KEY is set)\"
              COMPONENTS=\"$$COMPONENTS,traefik-log-dashboard\"
            fi
            export COMPONENTS
            echo \"[orchestrator] Auto-derived COMPONENTS: $$COMPONENTS\"
          else
            echo \"[orchestrator] Using provided COMPONENTS: $$COMPONENTS\"
          fi

          chmod +x /host-setup/orchestrator/build-compose.sh
          OUTPUT_DIR="/host-setup" COMPONENTS="$$COMPONENTS" /bin/bash /host-setup/orchestrator/build-compose.sh || exit 1
          echo '✅ Orchestrator finished.'
          # Exit to avoid running legacy monolithic generation
          exit 0
        fi




      "
    restart: "no"